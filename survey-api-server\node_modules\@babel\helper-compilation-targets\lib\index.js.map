{"version": 3, "names": ["_browserslist", "require", "_helperValidatorOption", "_lru<PERSON>ache", "_utils", "_targets", "_options", "_pretty", "_debug", "_filterItems", "browserModulesData", "ESM_SUPPORT", "v", "OptionValidator", "validateTargetNames", "targets", "validTargets", "Object", "keys", "TargetNames", "target", "Error", "formatMessage", "findSuggestion", "isBrowsersQueryValid", "browsers", "Array", "isArray", "every", "b", "validateBrowsers", "invariant", "undefined", "String", "getLowestVersions", "reduce", "all", "browser", "browserName", "browserVersion", "split", "browserNameMap", "splitVersion", "toLowerCase", "isSplitUnreleased", "isUnreleasedVersion", "semverify", "version", "isUnreleased", "getLowestUnreleased", "parsedBrowserVersion", "semverMin", "_", "outputDecimalWarning", "decimalTargets", "length", "console", "warn", "for<PERSON>ach", "value", "semverifyTarget", "node<PERSON>arget<PERSON><PERSON><PERSON>", "parsed", "process", "versions", "node", "defaultTargetParser", "generateTargets", "inputTargets", "input", "assign", "<PERSON><PERSON><PERSON><PERSON>", "resolveTargets", "queries", "env", "resolved", "browserslist", "mobileToDesktop", "targetsCache", "<PERSON><PERSON><PERSON><PERSON>", "max", "resolveTargetsCached", "cache<PERSON>ey", "join", "cached", "get", "set", "getTargets", "options", "_browsers", "_browsers2", "config<PERSON><PERSON>", "onBrowserslistConfigFound", "shouldParseBrowsers", "hasTargets", "shouldSearchForConfig", "ignoreBrowserslistConfig", "BROWSERSLIST", "configFile", "BROWSERSLIST_CONFIG", "findConfigFile", "loadConfig", "config", "browserslistEnv", "map", "queryBrowsers", "esmSupportVersion", "getHighestUnreleased", "result", "decimalWarnings", "sort", "push", "parsed<PERSON><PERSON>get", "parsedValue"], "sources": ["../src/index.ts"], "sourcesContent": ["import browserslist from \"browserslist\";\nimport { findSuggestion } from \"@babel/helper-validator-option\";\nimport browserModulesData from \"@babel/compat-data/native-modules\" with { type: \"json\" };\nimport LruCache from \"lru-cache\";\n\nimport {\n  semverify,\n  semverMin,\n  isUnreleasedVersion,\n  getLowestUnreleased,\n  getHighestUnreleased,\n} from \"./utils.ts\";\nimport { OptionValidator } from \"@babel/helper-validator-option\";\nimport { browserNameMap } from \"./targets.ts\";\nimport { TargetNames } from \"./options.ts\";\nimport type {\n  Target,\n  Targets,\n  InputTargets,\n  Browsers,\n  BrowserslistBrowserName,\n  TargetsTuple,\n} from \"./types.d.ts\";\n\nexport type { Target, Targets, InputTargets };\n\nexport { prettifyTargets } from \"./pretty.ts\";\nexport { getInclusionReasons } from \"./debug.ts\";\nexport { default as filterItems, isRequired } from \"./filter-items.ts\";\nexport { unreleasedLabels } from \"./targets.ts\";\nexport { TargetNames };\n\nconst ESM_SUPPORT = browserModulesData[\"es6.module\"];\n\nconst v = new OptionValidator(PACKAGE_JSON.name);\n\nfunction validateTargetNames(targets: Targets): TargetsTuple {\n  const validTargets = Object.keys(TargetNames);\n  for (const target of Object.keys(targets)) {\n    if (!(target in TargetNames)) {\n      throw new Error(\n        v.formatMessage(`'${target}' is not a valid target\n- Did you mean '${findSuggestion(target, validTargets)}'?`),\n      );\n    }\n  }\n\n  return targets;\n}\n\nexport function isBrowsersQueryValid(browsers: unknown): boolean {\n  return (\n    typeof browsers === \"string\" ||\n    (Array.isArray(browsers) && browsers.every(b => typeof b === \"string\"))\n  );\n}\n\nfunction validateBrowsers(browsers: Browsers | undefined) {\n  v.invariant(\n    browsers === undefined || isBrowsersQueryValid(browsers),\n    `'${String(browsers)}' is not a valid browserslist query`,\n  );\n\n  return browsers;\n}\n\nfunction getLowestVersions(browsers: Array<string>): Targets {\n  return browsers.reduce(\n    (all, browser) => {\n      const [browserName, browserVersion] = browser.split(\" \") as [\n        BrowserslistBrowserName,\n        string,\n      ];\n      const target = browserNameMap[browserName];\n\n      if (!target) {\n        return all;\n      }\n\n      try {\n        // Browser version can return as \"10.0-10.2\"\n        const splitVersion = browserVersion.split(\"-\")[0].toLowerCase();\n        const isSplitUnreleased = isUnreleasedVersion(splitVersion, target);\n\n        if (!all[target]) {\n          all[target] = isSplitUnreleased\n            ? splitVersion\n            : semverify(splitVersion);\n          return all;\n        }\n\n        const version = all[target];\n        const isUnreleased = isUnreleasedVersion(version, target);\n\n        if (isUnreleased && isSplitUnreleased) {\n          all[target] = getLowestUnreleased(version, splitVersion, target);\n        } else if (isUnreleased) {\n          all[target] = semverify(splitVersion);\n        } else if (!isUnreleased && !isSplitUnreleased) {\n          const parsedBrowserVersion = semverify(splitVersion);\n\n          all[target] = semverMin(version, parsedBrowserVersion);\n        }\n      } catch (_) {}\n\n      return all;\n    },\n    {} as Record<Target, string>,\n  );\n}\n\nfunction outputDecimalWarning(\n  decimalTargets: Array<{ target: string; value: number }>,\n) {\n  if (!decimalTargets.length) {\n    return;\n  }\n\n  console.warn(\"Warning, the following targets are using a decimal version:\\n\");\n  decimalTargets.forEach(({ target, value }) =>\n    console.warn(`  ${target}: ${value}`),\n  );\n  console.warn(`\nWe recommend using a string for minor/patch versions to avoid numbers like 6.10\ngetting parsed as 6.1, which can lead to unexpected behavior.\n`);\n}\n\nfunction semverifyTarget(target: Target, value: string) {\n  try {\n    return semverify(value);\n  } catch (_) {\n    throw new Error(\n      v.formatMessage(\n        `'${value}' is not a valid value for 'targets.${target}'.`,\n      ),\n    );\n  }\n}\n\n// Parse `node: true` and `node: \"current\"` to version\nfunction nodeTargetParser(value: true | string) {\n  const parsed =\n    value === true || value === \"current\"\n      ? // Align with `browserslist` and strip prerelease tag.\n        process.versions.node.split(\"-\")[0]\n      : semverifyTarget(\"node\", value);\n  return [\"node\", parsed] as const;\n}\n\nfunction defaultTargetParser(\n  target: Exclude<Target, \"node\">,\n  value: string,\n): readonly [Exclude<Target, \"node\">, string] {\n  const version = isUnreleasedVersion(value, target)\n    ? value.toLowerCase()\n    : semverifyTarget(target, value);\n  return [target, version] as const;\n}\n\nfunction generateTargets(inputTargets: InputTargets): Targets {\n  const input = { ...inputTargets };\n  delete input.esmodules;\n  delete input.browsers;\n  return input;\n}\n\nfunction resolveTargets(queries: Browsers, env?: string): Targets {\n  const resolved = browserslist(queries, {\n    mobileToDesktop: true,\n    env,\n  });\n  return getLowestVersions(resolved);\n}\n\nconst targetsCache = new LruCache({ max: 64 });\n\nfunction resolveTargetsCached(queries: Browsers, env?: string): Targets {\n  const cacheKey = typeof queries === \"string\" ? queries : queries.join() + env;\n  let cached = targetsCache.get(cacheKey) as Targets | undefined;\n  if (!cached) {\n    cached = resolveTargets(queries, env);\n    targetsCache.set(cacheKey, cached);\n  }\n  return { ...cached };\n}\n\ntype GetTargetsOption = {\n  // This is not the path of the config file, but the path where start searching it from\n  configPath?: string;\n  // The path of the config file\n  configFile?: string;\n  // The env to pass to browserslist\n  browserslistEnv?: string;\n  // true to disable config loading\n  ignoreBrowserslistConfig?: boolean;\n  // custom hook when browserslist config is found\n  onBrowserslistConfigFound?: (configFile: string) => void;\n};\n\nexport default function getTargets(\n  inputTargets: InputTargets = {},\n  options: GetTargetsOption = {},\n): Targets {\n  let { browsers, esmodules } = inputTargets;\n  const { configPath = \".\", onBrowserslistConfigFound } = options;\n\n  validateBrowsers(browsers);\n\n  const input = generateTargets(inputTargets);\n  let targets = validateTargetNames(input);\n\n  const shouldParseBrowsers = !!browsers;\n  const hasTargets = shouldParseBrowsers || Object.keys(targets).length > 0;\n  const shouldSearchForConfig =\n    !options.ignoreBrowserslistConfig && !hasTargets;\n\n  if (!browsers && shouldSearchForConfig) {\n    // https://github.com/browserslist/browserslist/blob/8ae85caa905d130f4ca86f7a998a5b63abbbe582/node.js#L243\n    browsers = process.env.BROWSERSLIST;\n    if (!browsers) {\n      const configFile =\n        options.configFile ||\n        process.env.BROWSERSLIST_CONFIG ||\n        browserslist.findConfigFile(configPath);\n      if (configFile != null) {\n        onBrowserslistConfigFound?.(configFile);\n        browsers = browserslist.loadConfig({\n          config: configFile,\n          env: options.browserslistEnv,\n        });\n      }\n    }\n\n    if (browsers == null) {\n      if (process.env.BABEL_8_BREAKING) {\n        // In Babel 8, if no targets are passed, we use browserslist's defaults.\n        browsers = [\"defaults\"];\n      } else {\n        // If no targets are passed, we need to overwrite browserslist's defaults\n        // so that we enable all transforms (acting like the now deprecated\n        // preset-latest).\n        browsers = [];\n      }\n    }\n  }\n\n  if (process.env.BABEL_8_BREAKING && esmodules) {\n    esmodules = \"intersect\";\n  }\n\n  // `esmodules` as a target indicates the specific set of browsers supporting ES Modules.\n  // These values OVERRIDE the `browsers` field.\n  if (esmodules && (esmodules !== \"intersect\" || !browsers?.length)) {\n    browsers = Object.keys(ESM_SUPPORT)\n      .map(\n        (browser: keyof typeof ESM_SUPPORT) =>\n          `${browser} >= ${ESM_SUPPORT[browser]}`,\n      )\n      .join(\", \");\n    esmodules = false;\n  }\n\n  // If current value of `browsers` is undefined (`ignoreBrowserslistConfig` should be `false`)\n  // or an empty array (without any user config, use default config),\n  // we don't need to call `resolveTargets` to execute the related methods of `browserslist` library.\n  if (browsers?.length) {\n    const queryBrowsers = resolveTargetsCached(\n      browsers,\n      options.browserslistEnv,\n    );\n\n    if (esmodules === \"intersect\") {\n      for (const browser of Object.keys(queryBrowsers) as Target[]) {\n        if (browser !== \"deno\" && browser !== \"ie\") {\n          const esmSupportVersion =\n            ESM_SUPPORT[browser === \"opera_mobile\" ? \"op_mob\" : browser];\n\n          if (esmSupportVersion) {\n            const version = queryBrowsers[browser];\n            queryBrowsers[browser] = getHighestUnreleased(\n              version,\n              semverify(esmSupportVersion),\n              browser,\n            );\n          } else {\n            delete queryBrowsers[browser];\n          }\n        } else {\n          delete queryBrowsers[browser];\n        }\n      }\n    }\n\n    targets = Object.assign(queryBrowsers, targets);\n  }\n\n  // Parse remaining targets\n  const result: Targets = {};\n  const decimalWarnings = [];\n  for (const target of Object.keys(targets).sort() as Target[]) {\n    const value = targets[target];\n\n    // Warn when specifying minor/patch as a decimal\n    if (typeof value === \"number\" && value % 1 !== 0) {\n      decimalWarnings.push({ target, value });\n    }\n\n    const [parsedTarget, parsedValue] =\n      target === \"node\"\n        ? nodeTargetParser(value)\n        : defaultTargetParser(target, value as string);\n\n    if (parsedValue) {\n      // Merge (lowest wins)\n      result[parsedTarget] = parsedValue;\n    }\n  }\n\n  outputDecimalWarning(decimalWarnings);\n\n  return result;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,aAAA,GAAAC,OAAA;AACA,IAAAC,sBAAA,GAAAD,OAAA;AAEA,IAAAE,SAAA,GAAAF,OAAA;AAEA,IAAAG,MAAA,GAAAH,OAAA;AAQA,IAAAI,QAAA,GAAAJ,OAAA;AACA,IAAAK,QAAA,GAAAL,OAAA;AAYA,IAAAM,OAAA,GAAAN,OAAA;AACA,IAAAO,MAAA,GAAAP,OAAA;AACA,IAAAQ,YAAA,GAAAR,OAAA;AAAuE,MA1BhES,kBAAkB,GAAAT,OAAA,CAAM,mCAAmC;AA8BlE,MAAMU,WAAW,GAAGD,kBAAkB,CAAC,YAAY,CAAC;AAEpD,MAAME,CAAC,GAAG,IAAIC,sCAAe,oCAAkB,CAAC;AAEhD,SAASC,mBAAmBA,CAACC,OAAgB,EAAgB;EAC3D,MAAMC,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACC,oBAAW,CAAC;EAC7C,KAAK,MAAMC,MAAM,IAAIH,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,EAAE;IACzC,IAAI,EAAEK,MAAM,IAAID,oBAAW,CAAC,EAAE;MAC5B,MAAM,IAAIE,KAAK,CACbT,CAAC,CAACU,aAAa,CAAC,IAAIF,MAAM;AAClC,kBAAkB,IAAAG,qCAAc,EAACH,MAAM,EAAEJ,YAAY,CAAC,IAAI,CACpD,CAAC;IACH;EACF;EAEA,OAAOD,OAAO;AAChB;AAEO,SAASS,oBAAoBA,CAACC,QAAiB,EAAW;EAC/D,OACE,OAAOA,QAAQ,KAAK,QAAQ,IAC3BC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,IAAIA,QAAQ,CAACG,KAAK,CAACC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,CAAE;AAE3E;AAEA,SAASC,gBAAgBA,CAACL,QAA8B,EAAE;EACxDb,CAAC,CAACmB,SAAS,CACTN,QAAQ,KAAKO,SAAS,IAAIR,oBAAoB,CAACC,QAAQ,CAAC,EACxD,IAAIQ,MAAM,CAACR,QAAQ,CAAC,qCACtB,CAAC;EAED,OAAOA,QAAQ;AACjB;AAEA,SAASS,iBAAiBA,CAACT,QAAuB,EAAW;EAC3D,OAAOA,QAAQ,CAACU,MAAM,CACpB,CAACC,GAAG,EAAEC,OAAO,KAAK;IAChB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGF,OAAO,CAACG,KAAK,CAAC,GAAG,CAGtD;IACD,MAAMpB,MAAM,GAAGqB,uBAAc,CAACH,WAAW,CAAC;IAE1C,IAAI,CAAClB,MAAM,EAAE;MACX,OAAOgB,GAAG;IACZ;IAEA,IAAI;MAEF,MAAMM,YAAY,GAAGH,cAAc,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC;MAC/D,MAAMC,iBAAiB,GAAG,IAAAC,0BAAmB,EAACH,YAAY,EAAEtB,MAAM,CAAC;MAEnE,IAAI,CAACgB,GAAG,CAAChB,MAAM,CAAC,EAAE;QAChBgB,GAAG,CAAChB,MAAM,CAAC,GAAGwB,iBAAiB,GAC3BF,YAAY,GACZ,IAAAI,gBAAS,EAACJ,YAAY,CAAC;QAC3B,OAAON,GAAG;MACZ;MAEA,MAAMW,OAAO,GAAGX,GAAG,CAAChB,MAAM,CAAC;MAC3B,MAAM4B,YAAY,GAAG,IAAAH,0BAAmB,EAACE,OAAO,EAAE3B,MAAM,CAAC;MAEzD,IAAI4B,YAAY,IAAIJ,iBAAiB,EAAE;QACrCR,GAAG,CAAChB,MAAM,CAAC,GAAG,IAAA6B,0BAAmB,EAACF,OAAO,EAAEL,YAAY,EAAEtB,MAAM,CAAC;MAClE,CAAC,MAAM,IAAI4B,YAAY,EAAE;QACvBZ,GAAG,CAAChB,MAAM,CAAC,GAAG,IAAA0B,gBAAS,EAACJ,YAAY,CAAC;MACvC,CAAC,MAAM,IAAI,CAACM,YAAY,IAAI,CAACJ,iBAAiB,EAAE;QAC9C,MAAMM,oBAAoB,GAAG,IAAAJ,gBAAS,EAACJ,YAAY,CAAC;QAEpDN,GAAG,CAAChB,MAAM,CAAC,GAAG,IAAA+B,gBAAS,EAACJ,OAAO,EAAEG,oBAAoB,CAAC;MACxD;IACF,CAAC,CAAC,OAAOE,CAAC,EAAE,CAAC;IAEb,OAAOhB,GAAG;EACZ,CAAC,EACD,CAAC,CACH,CAAC;AACH;AAEA,SAASiB,oBAAoBA,CAC3BC,cAAwD,EACxD;EACA,IAAI,CAACA,cAAc,CAACC,MAAM,EAAE;IAC1B;EACF;EAEAC,OAAO,CAACC,IAAI,CAAC,+DAA+D,CAAC;EAC7EH,cAAc,CAACI,OAAO,CAAC,CAAC;IAAEtC,MAAM;IAAEuC;EAAM,CAAC,KACvCH,OAAO,CAACC,IAAI,CAAC,KAAKrC,MAAM,KAAKuC,KAAK,EAAE,CACtC,CAAC;EACDH,OAAO,CAACC,IAAI,CAAC;AACf;AACA;AACA,CAAC,CAAC;AACF;AAEA,SAASG,eAAeA,CAACxC,MAAc,EAAEuC,KAAa,EAAE;EACtD,IAAI;IACF,OAAO,IAAAb,gBAAS,EAACa,KAAK,CAAC;EACzB,CAAC,CAAC,OAAOP,CAAC,EAAE;IACV,MAAM,IAAI/B,KAAK,CACbT,CAAC,CAACU,aAAa,CACb,IAAIqC,KAAK,uCAAuCvC,MAAM,IACxD,CACF,CAAC;EACH;AACF;AAGA,SAASyC,gBAAgBA,CAACF,KAAoB,EAAE;EAC9C,MAAMG,MAAM,GACVH,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,SAAS,GAEjCI,OAAO,CAACC,QAAQ,CAACC,IAAI,CAACzB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GACnCoB,eAAe,CAAC,MAAM,EAAED,KAAK,CAAC;EACpC,OAAO,CAAC,MAAM,EAAEG,MAAM,CAAC;AACzB;AAEA,SAASI,mBAAmBA,CAC1B9C,MAA+B,EAC/BuC,KAAa,EAC+B;EAC5C,MAAMZ,OAAO,GAAG,IAAAF,0BAAmB,EAACc,KAAK,EAAEvC,MAAM,CAAC,GAC9CuC,KAAK,CAAChB,WAAW,CAAC,CAAC,GACnBiB,eAAe,CAACxC,MAAM,EAAEuC,KAAK,CAAC;EAClC,OAAO,CAACvC,MAAM,EAAE2B,OAAO,CAAC;AAC1B;AAEA,SAASoB,eAAeA,CAACC,YAA0B,EAAW;EAC5D,MAAMC,KAAK,GAAApD,MAAA,CAAAqD,MAAA,KAAQF,YAAY,CAAE;EACjC,OAAOC,KAAK,CAACE,SAAS;EACtB,OAAOF,KAAK,CAAC5C,QAAQ;EACrB,OAAO4C,KAAK;AACd;AAEA,SAASG,cAAcA,CAACC,OAAiB,EAAEC,GAAY,EAAW;EAChE,MAAMC,QAAQ,GAAGC,aAAY,CAACH,OAAO,EAAE;IACrCI,eAAe,EAAE,IAAI;IACrBH;EACF,CAAC,CAAC;EACF,OAAOxC,iBAAiB,CAACyC,QAAQ,CAAC;AACpC;AAEA,MAAMG,YAAY,GAAG,IAAIC,SAAQ,CAAC;EAAEC,GAAG,EAAE;AAAG,CAAC,CAAC;AAE9C,SAASC,oBAAoBA,CAACR,OAAiB,EAAEC,GAAY,EAAW;EACtE,MAAMQ,QAAQ,GAAG,OAAOT,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGA,OAAO,CAACU,IAAI,CAAC,CAAC,GAAGT,GAAG;EAC7E,IAAIU,MAAM,GAAGN,YAAY,CAACO,GAAG,CAACH,QAAQ,CAAwB;EAC9D,IAAI,CAACE,MAAM,EAAE;IACXA,MAAM,GAAGZ,cAAc,CAACC,OAAO,EAAEC,GAAG,CAAC;IACrCI,YAAY,CAACQ,GAAG,CAACJ,QAAQ,EAAEE,MAAM,CAAC;EACpC;EACA,OAAAnE,MAAA,CAAAqD,MAAA,KAAYc,MAAM;AACpB;AAee,SAASG,UAAUA,CAChCnB,YAA0B,GAAG,CAAC,CAAC,EAC/BoB,OAAyB,GAAG,CAAC,CAAC,EACrB;EAAA,IAAAC,SAAA,EAAAC,UAAA;EACT,IAAI;IAAEjE,QAAQ;IAAE8C;EAAU,CAAC,GAAGH,YAAY;EAC1C,MAAM;IAAEuB,UAAU,GAAG,GAAG;IAAEC;EAA0B,CAAC,GAAGJ,OAAO;EAE/D1D,gBAAgB,CAACL,QAAQ,CAAC;EAE1B,MAAM4C,KAAK,GAAGF,eAAe,CAACC,YAAY,CAAC;EAC3C,IAAIrD,OAAO,GAAGD,mBAAmB,CAACuD,KAAK,CAAC;EAExC,MAAMwB,mBAAmB,GAAG,CAAC,CAACpE,QAAQ;EACtC,MAAMqE,UAAU,GAAGD,mBAAmB,IAAI5E,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAACwC,MAAM,GAAG,CAAC;EACzE,MAAMwC,qBAAqB,GACzB,CAACP,OAAO,CAACQ,wBAAwB,IAAI,CAACF,UAAU;EAElD,IAAI,CAACrE,QAAQ,IAAIsE,qBAAqB,EAAE;IAEtCtE,QAAQ,GAAGsC,OAAO,CAACW,GAAG,CAACuB,YAAY;IACnC,IAAI,CAACxE,QAAQ,EAAE;MACb,MAAMyE,UAAU,GACdV,OAAO,CAACU,UAAU,IAClBnC,OAAO,CAACW,GAAG,CAACyB,mBAAmB,IAC/BvB,aAAY,CAACwB,cAAc,CAACT,UAAU,CAAC;MACzC,IAAIO,UAAU,IAAI,IAAI,EAAE;QACtBN,yBAAyB,YAAzBA,yBAAyB,CAAGM,UAAU,CAAC;QACvCzE,QAAQ,GAAGmD,aAAY,CAACyB,UAAU,CAAC;UACjCC,MAAM,EAAEJ,UAAU;UAClBxB,GAAG,EAAEc,OAAO,CAACe;QACf,CAAC,CAAC;MACJ;IACF;IAEA,IAAI9E,QAAQ,IAAI,IAAI,EAAE;MAIb;QAILA,QAAQ,GAAG,EAAE;MACf;IACF;EACF;EAAC;EAQD,IAAI8C,SAAS,KAAKA,SAAS,KAAK,WAAW,IAAI,GAAAkB,SAAA,GAAChE,QAAQ,aAARgE,SAAA,CAAUlC,MAAM,EAAC,EAAE;IACjE9B,QAAQ,GAAGR,MAAM,CAACC,IAAI,CAACP,WAAW,CAAC,CAChC6F,GAAG,CACDnE,OAAiC,IAChC,GAAGA,OAAO,OAAO1B,WAAW,CAAC0B,OAAO,CAAC,EACzC,CAAC,CACA8C,IAAI,CAAC,IAAI,CAAC;IACbZ,SAAS,GAAG,KAAK;EACnB;EAKA,KAAAmB,UAAA,GAAIjE,QAAQ,aAARiE,UAAA,CAAUnC,MAAM,EAAE;IACpB,MAAMkD,aAAa,GAAGxB,oBAAoB,CACxCxD,QAAQ,EACR+D,OAAO,CAACe,eACV,CAAC;IAED,IAAIhC,SAAS,KAAK,WAAW,EAAE;MAC7B,KAAK,MAAMlC,OAAO,IAAIpB,MAAM,CAACC,IAAI,CAACuF,aAAa,CAAC,EAAc;QAC5D,IAAIpE,OAAO,KAAK,MAAM,IAAIA,OAAO,KAAK,IAAI,EAAE;UAC1C,MAAMqE,iBAAiB,GACrB/F,WAAW,CAAC0B,OAAO,KAAK,cAAc,GAAG,QAAQ,GAAGA,OAAO,CAAC;UAE9D,IAAIqE,iBAAiB,EAAE;YACrB,MAAM3D,OAAO,GAAG0D,aAAa,CAACpE,OAAO,CAAC;YACtCoE,aAAa,CAACpE,OAAO,CAAC,GAAG,IAAAsE,2BAAoB,EAC3C5D,OAAO,EACP,IAAAD,gBAAS,EAAC4D,iBAAiB,CAAC,EAC5BrE,OACF,CAAC;UACH,CAAC,MAAM;YACL,OAAOoE,aAAa,CAACpE,OAAO,CAAC;UAC/B;QACF,CAAC,MAAM;UACL,OAAOoE,aAAa,CAACpE,OAAO,CAAC;QAC/B;MACF;IACF;IAEAtB,OAAO,GAAGE,MAAM,CAACqD,MAAM,CAACmC,aAAa,EAAE1F,OAAO,CAAC;EACjD;EAGA,MAAM6F,MAAe,GAAG,CAAC,CAAC;EAC1B,MAAMC,eAAe,GAAG,EAAE;EAC1B,KAAK,MAAMzF,MAAM,IAAIH,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAAC+F,IAAI,CAAC,CAAC,EAAc;IAC5D,MAAMnD,KAAK,GAAG5C,OAAO,CAACK,MAAM,CAAC;IAG7B,IAAI,OAAOuC,KAAK,KAAK,QAAQ,IAAIA,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE;MAChDkD,eAAe,CAACE,IAAI,CAAC;QAAE3F,MAAM;QAAEuC;MAAM,CAAC,CAAC;IACzC;IAEA,MAAM,CAACqD,YAAY,EAAEC,WAAW,CAAC,GAC/B7F,MAAM,KAAK,MAAM,GACbyC,gBAAgB,CAACF,KAAK,CAAC,GACvBO,mBAAmB,CAAC9C,MAAM,EAAEuC,KAAe,CAAC;IAElD,IAAIsD,WAAW,EAAE;MAEfL,MAAM,CAACI,YAAY,CAAC,GAAGC,WAAW;IACpC;EACF;EAEA5D,oBAAoB,CAACwD,eAAe,CAAC;EAErC,OAAOD,MAAM;AACf", "ignoreList": []}